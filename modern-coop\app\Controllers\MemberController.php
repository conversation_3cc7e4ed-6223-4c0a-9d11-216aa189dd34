<?php

namespace App\Controllers;

use App\Models\Member;
use App\Models\MemberType;

/**
 * Member Controller
 * จัดการ CRUD operations สำหรับสมาชิกสหกรณ์
 */
class MemberController extends BaseController
{
    private $memberModel;
    private $memberTypeModel;
    
    public function __construct()
    {
        parent::__construct();
        $this->memberModel = new Member();
        $this->memberTypeModel = new MemberType();
    }
    
    /**
     * Get all members with pagination
     * GET /api/members
     */
    public function index()
    {
        $this->requirePermission('1', 'VIEW'); // Menu code for member management
        
        try {
            [$page, $perPage] = $this->getPaginationParams();
            $searchParams = $this->getSearchParams();
            
            if (!empty($searchParams['search'])) {
                $result = $this->memberModel->searchMembers($searchParams['search'], $page, $perPage);
            } else {
                $result = $this->memberModel->getActiveMembers($page, $perPage);
            }
            
            $this->logActivity('VIEW_MEMBERS', "Page {$page}");
            return $this->successResponse($result);
            
        } catch (\Exception $e) {
            return $this->errorResponse('Failed to fetch members: ' . $e->getMessage());
        }
    }
    
    /**
     * Get single member by ID
     * GET /api/members/{id}
     */
    public function show($id)
    {
        $this->requirePermission('1', 'VIEW');
        
        try {
            $member = $this->memberModel->getMemberWithDetails($id);
            
            if (!$member) {
                return $this->errorResponse('Member not found', 404);
            }
            
            $this->logActivity('VIEW_MEMBER', "Member ID: {$id}");
            return $this->successResponse($member);
            
        } catch (\Exception $e) {
            return $this->errorResponse('Failed to fetch member: ' . $e->getMessage());
        }
    }
    
    /**
     * Create new member
     * POST /api/members
     */
    public function store()
    {
        $this->requirePermission('1', 'ADD');
        $this->validateCSRF();
        
        try {
            $input = $this->sanitizeInput($this->getInput());
            
            // Validation rules
            $rules = [
                'MemberCode' => 'required|string|max:20',
                'MemberName' => 'required|string|max:255',
                'MemberTypeID' => 'required|integer',
                'StudentNo' => 'string|max:20',
                'AmountOfShare' => 'numeric|min:0',
                'PriceOfShare' => 'numeric|min:0',
                'Debit' => 'numeric|min:0'
            ];
            
            $validation = $this->validateInput($input, $rules);
            if (!$validation['valid']) {
                return $this->errorResponse('Validation failed', 400, $validation['errors']);
            }
            
            // Calculate total price of share
            $amountOfShare = (float) ($input['AmountOfShare'] ?? 0);
            $priceOfShare = (float) ($input['PriceOfShare'] ?? 0);
            $input['TotalPriceOfShare'] = $amountOfShare * $priceOfShare;
            
            $member = $this->memberModel->createMember($input);
            
            $this->logActivity('CREATE_MEMBER', "Member Code: {$input['MemberCode']}");
            return $this->successResponse($member, 'Member created successfully');
            
        } catch (\Exception $e) {
            return $this->errorResponse('Failed to create member: ' . $e->getMessage());
        }
    }
    
    /**
     * Update member
     * PUT /api/members/{id}
     */
    public function update($id)
    {
        $this->requirePermission('1', 'EDIT');
        $this->validateCSRF();
        
        try {
            $input = $this->sanitizeInput($this->getInput());
            
            // Check if member exists
            $existingMember = $this->memberModel->find($id);
            if (!$existingMember) {
                return $this->errorResponse('Member not found', 404);
            }
            
            // Validation rules
            $rules = [
                'MemberCode' => 'string|max:20',
                'MemberName' => 'string|max:255',
                'MemberTypeID' => 'integer',
                'StudentNo' => 'string|max:20',
                'AmountOfShare' => 'numeric|min:0',
                'PriceOfShare' => 'numeric|min:0',
                'Debit' => 'numeric|min:0'
            ];
            
            $validation = $this->validateInput($input, $rules);
            if (!$validation['valid']) {
                return $this->errorResponse('Validation failed', 400, $validation['errors']);
            }
            
            // Recalculate total price if share data changed
            if (isset($input['AmountOfShare']) || isset($input['PriceOfShare'])) {
                $amountOfShare = (float) ($input['AmountOfShare'] ?? $existingMember['AmountOfShare']);
                $priceOfShare = (float) ($input['PriceOfShare'] ?? $existingMember['PriceOfShare']);
                $input['TotalPriceOfShare'] = $amountOfShare * $priceOfShare;
            }
            
            $member = $this->memberModel->updateMember($id, $input);
            
            $this->logActivity('UPDATE_MEMBER', "Member ID: {$id}");
            return $this->successResponse($member, 'Member updated successfully');
            
        } catch (\Exception $e) {
            return $this->errorResponse('Failed to update member: ' . $e->getMessage());
        }
    }
    
    /**
     * Delete member
     * DELETE /api/members/{id}
     */
    public function destroy($id)
    {
        $this->requirePermission('1', 'DELETE');
        $this->validateCSRF();
        
        try {
            $member = $this->memberModel->find($id);
            if (!$member) {
                return $this->errorResponse('Member not found', 404);
            }
            
            $deleted = $this->memberModel->delete($id);
            
            if ($deleted) {
                $this->logActivity('DELETE_MEMBER', "Member ID: {$id}, Code: {$member['MemberCode']}");
                return $this->successResponse(null, 'Member deleted successfully');
            } else {
                return $this->errorResponse('Failed to delete member');
            }
            
        } catch (\Exception $e) {
            return $this->errorResponse('Failed to delete member: ' . $e->getMessage());
        }
    }
    
    /**
     * Search members by code or name
     * GET /api/members/search
     */
    public function search()
    {
        $this->requirePermission('1', 'VIEW');
        
        try {
            $input = $this->getInput();
            $searchTerm = $this->sanitizeInput($input['q'] ?? '');
            
            if (empty($searchTerm)) {
                return $this->errorResponse('Search term is required');
            }
            
            [$page, $perPage] = $this->getPaginationParams();
            $result = $this->memberModel->searchMembers($searchTerm, $page, $perPage);
            
            return $this->successResponse($result);
            
        } catch (\Exception $e) {
            return $this->errorResponse('Search failed: ' . $e->getMessage());
        }
    }
    
    /**
     * Get member by member code
     * GET /api/members/by-code/{code}
     */
    public function getByCode($code)
    {
        $this->requirePermission('1', 'VIEW');
        
        try {
            $member = $this->memberModel->findByMemberCode($code);
            
            if (!$member) {
                return $this->errorResponse('Member not found', 404);
            }
            
            return $this->successResponse($member);
            
        } catch (\Exception $e) {
            return $this->errorResponse('Failed to fetch member: ' . $e->getMessage());
        }
    }
    
    /**
     * Update member debit
     * POST /api/members/{id}/debit
     */
    public function updateDebit($id)
    {
        $this->requirePermission('1', 'EDIT');
        $this->validateCSRF();
        
        try {
            $input = $this->sanitizeInput($this->getInput());
            
            $rules = [
                'amount' => 'required|numeric|min:0',
                'operation' => 'required|in:add,subtract,set'
            ];
            
            $validation = $this->validateInput($input, $rules);
            if (!$validation['valid']) {
                return $this->errorResponse('Validation failed', 400, $validation['errors']);
            }
            
            $member = $this->memberModel->updateDebit($id, $input['amount'], $input['operation']);
            
            if ($member) {
                $this->logActivity('UPDATE_MEMBER_DEBIT', "Member ID: {$id}, Amount: {$input['amount']}, Operation: {$input['operation']}");
                return $this->successResponse($member, 'Member debit updated successfully');
            } else {
                return $this->errorResponse('Failed to update member debit');
            }
            
        } catch (\Exception $e) {
            return $this->errorResponse('Failed to update debit: ' . $e->getMessage());
        }
    }
    
    /**
     * Get member statistics
     * GET /api/members/stats
     */
    public function getStats()
    {
        $this->requirePermission('1', 'VIEW');
        
        try {
            $stats = $this->memberModel->getMemberStats();
            return $this->successResponse($stats);
            
        } catch (\Exception $e) {
            return $this->errorResponse('Failed to fetch statistics: ' . $e->getMessage());
        }
    }
    
    /**
     * Export members to Excel
     * GET /api/members/export
     */
    public function export()
    {
        $this->requirePermission('1', 'VIEW');
        
        try {
            // This would integrate with a library like PhpSpreadsheet
            $members = $this->memberModel->findAll(['Status' => 'เป็นสมาชิกอยู่'], 'MemberName ASC');
            
            // For now, return JSON data
            // In a real implementation, you'd generate and return an Excel file
            $this->logActivity('EXPORT_MEMBERS', 'Excel export');
            return $this->successResponse($members, 'Export data prepared');
            
        } catch (\Exception $e) {
            return $this->errorResponse('Export failed: ' . $e->getMessage());
        }
    }
}
