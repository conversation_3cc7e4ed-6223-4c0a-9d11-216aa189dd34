<?php

namespace App\Controllers;

use App\Services\AuthService;
use App\Services\ValidationService;

/**
 * Base Controller Class
 * ให้ functionality พื้นฐานสำหรับ controllers ทั้งหมด
 */
abstract class BaseController
{
    protected $authService;
    protected $validationService;
    
    public function __construct()
    {
        $this->authService = new AuthService();
        $this->validationService = new ValidationService();
        
        // Start session if not already started
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
    }
    
    /**
     * Return JSON response
     */
    protected function jsonResponse($data, $statusCode = 200)
    {
        http_response_code($statusCode);
        header('Content-Type: application/json');
        echo json_encode($data, JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    /**
     * Return success response
     */
    protected function successResponse($data = null, $message = 'Success')
    {
        $response = [
            'success' => true,
            'message' => $message
        ];
        
        if ($data !== null) {
            $response['data'] = $data;
        }
        
        return $this->jsonResponse($response);
    }
    
    /**
     * Return error response
     */
    protected function errorResponse($message, $statusCode = 400, $errors = null)
    {
        $response = [
            'success' => false,
            'message' => $message
        ];
        
        if ($errors !== null) {
            $response['errors'] = $errors;
        }
        
        return $this->jsonResponse($response, $statusCode);
    }
    
    /**
     * Get request input data
     */
    protected function getInput()
    {
        $contentType = $_SERVER['CONTENT_TYPE'] ?? '';
        
        if (strpos($contentType, 'application/json') !== false) {
            $input = json_decode(file_get_contents('php://input'), true);
            return $input ?? [];
        }
        
        return array_merge($_GET, $_POST);
    }
    
    /**
     * Validate required authentication
     */
    protected function requireAuth()
    {
        if (!$this->authService->isAuthenticated()) {
            $this->errorResponse('Authentication required', 401);
        }
    }
    
    /**
     * Check user permission
     */
    protected function requirePermission($menuCode, $action = 'VIEW')
    {
        $this->requireAuth();
        
        if (!$this->authService->hasPermission($menuCode, $action)) {
            $this->errorResponse('Insufficient permissions', 403);
        }
    }
    
    /**
     * Get current user
     */
    protected function getCurrentUser()
    {
        return $this->authService->getCurrentUser();
    }
    
    /**
     * Validate CSRF token
     */
    protected function validateCSRF()
    {
        $token = $this->getInput()['csrf_token'] ?? '';
        
        if (!$this->validationService->validateCSRFToken($token)) {
            $this->errorResponse('Invalid CSRF token', 403);
        }
    }
    
    /**
     * Sanitize input data
     */
    protected function sanitizeInput($data)
    {
        if (is_array($data)) {
            return array_map([$this, 'sanitizeInput'], $data);
        }
        
        return htmlspecialchars(trim($data), ENT_QUOTES, 'UTF-8');
    }
    
    /**
     * Validate input data
     */
    protected function validateInput($data, $rules)
    {
        return $this->validationService->validate($data, $rules);
    }
    
    /**
     * Handle pagination parameters
     */
    protected function getPaginationParams()
    {
        $input = $this->getInput();
        
        $page = max(1, (int) ($input['page'] ?? 1));
        $perPage = max(1, min(100, (int) ($input['per_page'] ?? 15)));
        
        return [$page, $perPage];
    }
    
    /**
     * Handle search parameters
     */
    protected function getSearchParams()
    {
        $input = $this->getInput();
        
        return [
            'search' => $this->sanitizeInput($input['search'] ?? ''),
            'sort_by' => $this->sanitizeInput($input['sort_by'] ?? ''),
            'sort_order' => in_array($input['sort_order'] ?? '', ['asc', 'desc']) ? $input['sort_order'] : 'asc'
        ];
    }
    
    /**
     * Log user activity
     */
    protected function logActivity($action, $details = null)
    {
        $user = $this->getCurrentUser();
        
        if ($user) {
            // Log to database or file
            error_log(sprintf(
                "[%s] User %s (%s) performed action: %s %s",
                date('Y-m-d H:i:s'),
                $user['UserID'],
                $user['UserLoginName'],
                $action,
                $details ? "- {$details}" : ''
            ));
        }
    }
    
    /**
     * Handle file upload
     */
    protected function handleFileUpload($fileKey, $allowedTypes = [], $maxSize = 5242880) // 5MB default
    {
        if (!isset($_FILES[$fileKey])) {
            return null;
        }
        
        $file = $_FILES[$fileKey];
        
        // Check for upload errors
        if ($file['error'] !== UPLOAD_ERR_OK) {
            throw new \Exception('File upload error: ' . $file['error']);
        }
        
        // Check file size
        if ($file['size'] > $maxSize) {
            throw new \Exception('File size exceeds maximum allowed size');
        }
        
        // Check file type
        if (!empty($allowedTypes)) {
            $fileType = mime_content_type($file['tmp_name']);
            if (!in_array($fileType, $allowedTypes)) {
                throw new \Exception('File type not allowed');
            }
        }
        
        // Generate unique filename
        $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
        $filename = uniqid() . '.' . $extension;
        
        return [
            'original_name' => $file['name'],
            'filename' => $filename,
            'tmp_name' => $file['tmp_name'],
            'size' => $file['size'],
            'type' => $file['type']
        ];
    }
    
    /**
     * Render view (if using traditional views)
     */
    protected function render($view, $data = [])
    {
        extract($data);
        
        $viewPath = __DIR__ . "/../../resources/views/{$view}.php";
        
        if (!file_exists($viewPath)) {
            throw new \Exception("View not found: {$view}");
        }
        
        ob_start();
        include $viewPath;
        $content = ob_get_clean();
        
        echo $content;
    }
}
