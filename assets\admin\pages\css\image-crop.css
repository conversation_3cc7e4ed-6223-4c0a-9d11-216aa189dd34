/* Apply these styles only when #preview-pane has
   been placed within the Jcrop widget */
#preview-pane {
  display: block;
  position: absolute;
  z-index: 2000;
  right: -200px;
  padding: 6px;
  border: 1px rgba(0, 0, 0, 0.4) solid;
  background-color: white;
  -webkit-border-radius: 6px;
  -moz-border-radius: 6px;
  border-radius: 6px;
  -webkit-box-shadow: 1px 1px 5px 2px rgba(0, 0, 0, 0.2);
  -moz-box-shadow: 1px 1px 5px 2px rgba(0, 0, 0, 0.2);
  box-shadow: 1px 1px 5px 2px rgba(0, 0, 0, 0.2);
}

@media (max-width: 1024px) {
  #preview-pane {
    right: 0;
  }
}
/* The Javascript code will set the aspect ratio of the crop
   area based on the size of the thumbnail preview,
   specified here */
#preview-pane .preview-container {
  width: 175px;
  height: 150px;
  overflow: hidden;
}

#demo7 {
  background-color: #eee;
  width: 500px;
  height: 330px;
  font-size: 24px;
  font-weight: 300;
  display: block;
}
