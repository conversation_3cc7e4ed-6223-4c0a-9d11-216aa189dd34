/***
News Page
***/
.news-page {
  padding-bottom: 20px;
}

.news-page h1 {
  margin-bottom: 20px;
}

.news-page h2 {
  font-size: 38.5px;
  margin-bottom: 20px;
}

.news-page .top-news {
  margin-top: 0;
}

/*News Feeds*/
.news-blocks {
  padding: 10px;
  margin-bottom: 10px;
  background: #faf6ea;
  border-top: solid 2px #faf6ea;
}

.news-blocks:hover {
  background: #fff;
  border-color: #78cff8;
  transition: all 0.4s ease-in-out 0s;
  -moz-transition: all 0.4s ease-in-out 0s;
  -webkit-transition: all 0.4s ease-in-out 0s;
}

.news-blocks h3 {
  margin: 0 0 5px 0;
  font-size: 23px;
  line-height: 32px;
}

.news-blocks h3 a {
  color: #000;
}

.news-blocks h3 a:hover {
  color: #78cff8;
  text-decoration: none;
}

.news-blocks p {
  overflow: hidden;
}

.news-blocks a.news-block-btn {
  color: #000;
  display: block;
  font-size: 14px;
  background: none;
  padding: 5px 10px 0;
  text-align: right;
  text-decoration: none;
}

.news-blocks a.news-block-btn i {
  margin-left: 3px;
}

.news-blocks a.news-block-btn:hover {
  text-decoration: none;
}

.news-blocks img.news-block-img {
  width: 70px;
  height: 70px;
  margin: 5px 0px 0 10px;
}

.news-blocks .news-block-tags {
  margin-bottom: 8px;
}

.news-blocks .news-block-tags strong {
  margin-right: 10px;
  font-weight: 400;
}

.news-blocks .news-block-tags em {
  font-style: normal;
}

/*News Item Page*/
.news-item-page {
  padding: 10px 0;
}

.blog-tag-data ul {
  margin-bottom: 5px;
}

.blog-tag-data li {
  padding: 0;
}

.blog-tag-data li i {
  color: #78cff8;
}

.blog-tag-data li a {
  padding: 0;
  color: #555;
  margin-right: 8px;
}

.blog-tag-data {
  margin-bottom: 10px;
}

.blog-tag-data img {
  margin-bottom: 12px;
}

.blog-tag-data ul.blog-tags a {
  background: #eee;
  padding: 1px 4px;
  margin: 0 4px 4px 0;
  display: inline-block;
}

.blog-tag-data ul.blog-tags a:hover {
  background: #ddd;
  text-decoration: none;
}

.blog-tag-data .blog-tag-data-inner {
  text-align: right;
}
