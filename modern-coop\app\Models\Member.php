<?php

namespace App\Models;

/**
 * Member Model
 * จัดการข้อมูลสมาชิกสหกรณ์
 */
class Member extends BaseModel
{
    protected $table = 'member';
    protected $primaryKey = 'MemberID';
    
    protected $fillable = [
        'MemberTypeID',
        'MemberCode',
        'MemberName',
        'LevelClassID',
        'StudentClassID',
        'StudentNo',
        'Status',
        'AmountOfShare',
        'PriceOfShare',
        'TotalPriceOfShare',
        'PriceOfVolunteer',
        'TotalPay',
        'Debit',
        'BudgetYear',
        'DividendStatus'
    ];
    
    protected $hidden = [];
    
    /**
     * Find member by member code
     */
    public function findByMemberCode($memberCode)
    {
        $sql = "SELECT * FROM {$this->table} WHERE MemberCode = :member_code LIMIT 1";
        $result = $this->db->fetchOne($sql, ['member_code' => $memberCode]);
        
        if ($result) {
            return $this->hideFields($result);
        }
        
        return null;
    }
    
    /**
     * Get active members
     */
    public function getActiveMembers($page = 1, $perPage = 15)
    {
        return $this->paginate($page, $perPage, ['Status' => 'เป็นสมาชิกอยู่']);
    }
    
    /**
     * Search members by name or code
     */
    public function searchMembers($searchTerm, $page = 1, $perPage = 15)
    {
        $sql = "SELECT * FROM {$this->table} 
                WHERE (MemberName LIKE :search OR MemberCode LIKE :search)
                AND Status = 'เป็นสมาชิกอยู่'
                ORDER BY MemberName
                LIMIT :limit OFFSET :offset";
        
        $offset = ($page - 1) * $perPage;
        $searchParam = "%{$searchTerm}%";
        
        $params = [
            'search' => $searchParam,
            'limit' => $perPage,
            'offset' => $offset
        ];
        
        $results = $this->db->fetchAll($sql, $params);
        
        // Count total for pagination
        $countSql = "SELECT COUNT(*) as count FROM {$this->table} 
                     WHERE (MemberName LIKE :search OR MemberCode LIKE :search)
                     AND Status = 'เป็นสมาชิกอยู่'";
        
        $countResult = $this->db->fetchOne($countSql, ['search' => $searchParam]);
        $total = (int) $countResult['count'];
        
        return [
            'data' => array_map([$this, 'hideFields'], $results),
            'total' => $total,
            'per_page' => $perPage,
            'current_page' => $page,
            'last_page' => ceil($total / $perPage)
        ];
    }
    
    /**
     * Get member with related data (joins)
     */
    public function getMemberWithDetails($memberId)
    {
        $sql = "SELECT 
                    m.*,
                    mt.MemberTypeName,
                    sc.StudentClassName,
                    lc.LevelClassName
                FROM {$this->table} m
                LEFT JOIN membertype mt ON m.MemberTypeID = mt.MemberTypeID
                LEFT JOIN studentclass sc ON m.StudentClassID = sc.StudentClassID
                LEFT JOIN levelclass lc ON m.LevelClassID = lc.LevelClassID
                WHERE m.{$this->primaryKey} = :member_id";
        
        $result = $this->db->fetchOne($sql, ['member_id' => $memberId]);
        
        if ($result) {
            return $this->hideFields($result);
        }
        
        return null;
    }
    
    /**
     * Get members by budget year
     */
    public function getMembersByBudgetYear($budgetYear)
    {
        return $this->findAll(['BudgetYear' => $budgetYear], 'MemberName ASC');
    }
    
    /**
     * Update member share information
     */
    public function updateShareInfo($memberId, $shareData)
    {
        $allowedFields = [
            'AmountOfShare',
            'PriceOfShare',
            'TotalPriceOfShare'
        ];
        
        $filteredData = array_intersect_key($shareData, array_flip($allowedFields));
        
        if (empty($filteredData)) {
            return false;
        }
        
        return $this->update($memberId, $filteredData);
    }
    
    /**
     * Update member debit
     */
    public function updateDebit($memberId, $amount, $operation = 'add')
    {
        $member = $this->find($memberId);
        if (!$member) {
            return false;
        }
        
        $currentDebit = (float) $member['Debit'];
        
        if ($operation === 'add') {
            $newDebit = $currentDebit + $amount;
        } elseif ($operation === 'subtract') {
            $newDebit = $currentDebit - $amount;
        } else {
            $newDebit = $amount; // set absolute value
        }
        
        // Ensure debit doesn't go negative
        $newDebit = max(0, $newDebit);
        
        return $this->update($memberId, ['Debit' => $newDebit]);
    }
    
    /**
     * Get member statistics
     */
    public function getMemberStats()
    {
        $sql = "SELECT 
                    COUNT(*) as total_members,
                    COUNT(CASE WHEN Status = 'เป็นสมาชิกอยู่' THEN 1 END) as active_members,
                    SUM(CASE WHEN Status = 'เป็นสมาชิกอยู่' THEN AmountOfShare ELSE 0 END) as total_shares,
                    SUM(CASE WHEN Status = 'เป็นสมาชิกอยู่' THEN TotalPriceOfShare ELSE 0 END) as total_share_value,
                    SUM(CASE WHEN Status = 'เป็นสมาชิกอยู่' THEN Debit ELSE 0 END) as total_debit
                FROM {$this->table}";
        
        return $this->db->fetchOne($sql);
    }
    
    /**
     * Validate member data before save
     */
    public function validateMemberData($data)
    {
        $errors = [];
        
        // Required fields
        $required = ['MemberCode', 'MemberName', 'MemberTypeID'];
        foreach ($required as $field) {
            if (empty($data[$field])) {
                $errors[] = "Field {$field} is required";
            }
        }
        
        // Check for duplicate member code
        if (!empty($data['MemberCode'])) {
            $existing = $this->findByMemberCode($data['MemberCode']);
            if ($existing && (!isset($data['MemberID']) || $existing['MemberID'] != $data['MemberID'])) {
                $errors[] = "Member code already exists";
            }
        }
        
        // Validate numeric fields
        $numericFields = ['AmountOfShare', 'PriceOfShare', 'TotalPriceOfShare', 'Debit'];
        foreach ($numericFields as $field) {
            if (isset($data[$field]) && !is_numeric($data[$field])) {
                $errors[] = "Field {$field} must be numeric";
            }
        }
        
        return $errors;
    }
    
    /**
     * Create member with validation
     */
    public function createMember($data)
    {
        $errors = $this->validateMemberData($data);
        if (!empty($errors)) {
            throw new \Exception("Validation failed: " . implode(', ', $errors));
        }
        
        // Set default values
        $data['Status'] = $data['Status'] ?? 'เป็นสมาชิกอยู่';
        $data['AmountOfShare'] = $data['AmountOfShare'] ?? 0;
        $data['Debit'] = $data['Debit'] ?? 0;
        
        return $this->create($data);
    }
    
    /**
     * Update member with validation
     */
    public function updateMember($memberId, $data)
    {
        $data['MemberID'] = $memberId; // For validation
        $errors = $this->validateMemberData($data);
        if (!empty($errors)) {
            throw new \Exception("Validation failed: " . implode(', ', $errors));
        }
        
        unset($data['MemberID']); // Remove from update data
        return $this->update($memberId, $data);
    }
}
