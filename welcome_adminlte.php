<?php
include('check_session.php');
include('lib/inc_database.php');
$dbconn =  new MysqlConn();

$firstLogin = false;
$db_query=$dbconn->getQuery("select count(*) as nCount from loginhistory where UserID = ".$_SESSION["CoopUserID"]);
if($result = mysql_fetch_array($db_query,MYSQL_ASSOC)){
	if($result["nCount"] == 1){
		$firstLogin = true;
	}
}
?>
<!DOCTYPE html>
<html lang="th">
<head>
<?php include('header_script_adminlte.php'); ?>
</head>
<body class="hold-transition sidebar-mini layout-fixed">
<div class="wrapper">

  <!-- Preloader -->
  <div class="preloader flex-column justify-content-center align-items-center">
    <img class="animation__shake" src="picture/logo/logo-small.png" alt="Logo" height="60" width="60">
  </div>

  <!-- Navbar -->
  <?php include('header_adminlte.php'); ?>

  <!-- Main Sidebar Container -->
  <?php include('menu_adminlte.php'); ?>

  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <div class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6">
            <h1 class="m-0">หน้าหลัก</h1>
          </div><!-- /.col -->
          <div class="col-sm-6">
            <ol class="breadcrumb float-sm-right">
              <li class="breadcrumb-item"><a href="#">หน้าหลัก</a></li>
              <li class="breadcrumb-item active">แดชบอร์ด</li>
            </ol>
          </div><!-- /.col -->
        </div><!-- /.row -->
      </div><!-- /.container-fluid -->
    </div>
    <!-- /.content-header -->

    <!-- Main content -->
    <section class="content">
      <div class="container-fluid">
        <!-- Small boxes (Stat box) -->
        <div class="row">
          <div class="col-lg-3 col-6">
            <!-- small box -->
            <div class="small-box bg-info">
              <div class="inner">
                <h3>150</h3>
                <p>สมาชิกใหม่</p>
              </div>
              <div class="icon">
                <i class="ion ion-bag"></i>
              </div>
              <a href="member.php" class="small-box-footer">ดูเพิ่มเติม <i class="fas fa-arrow-circle-right"></i></a>
            </div>
          </div>
          <!-- ./col -->
          <div class="col-lg-3 col-6">
            <!-- small box -->
            <div class="small-box bg-success">
              <div class="inner">
                <h3>53<sup style="font-size: 20px">%</sup></h3>
                <p>อัตราการเติบโต</p>
              </div>
              <div class="icon">
                <i class="ion ion-stats-bars"></i>
              </div>
              <a href="#" class="small-box-footer">ดูเพิ่มเติม <i class="fas fa-arrow-circle-right"></i></a>
            </div>
          </div>
          <!-- ./col -->
          <div class="col-lg-3 col-6">
            <!-- small box -->
            <div class="small-box bg-warning">
              <div class="inner">
                <h3>44</h3>
                <p>การลงทะเบียนผู้ใช้</p>
              </div>
              <div class="icon">
                <i class="ion ion-person-add"></i>
              </div>
              <a href="user.php" class="small-box-footer">ดูเพิ่มเติม <i class="fas fa-arrow-circle-right"></i></a>
            </div>
          </div>
          <!-- ./col -->
          <div class="col-lg-3 col-6">
            <!-- small box -->
            <div class="small-box bg-danger">
              <div class="inner">
                <h3>65</h3>
                <p>เข้าชมที่ไม่ซ้ำ</p>
              </div>
              <div class="icon">
                <i class="ion ion-pie-graph"></i>
              </div>
              <a href="#" class="small-box-footer">ดูเพิ่มเติม <i class="fas fa-arrow-circle-right"></i></a>
            </div>
          </div>
          <!-- ./col -->
        </div>
        <!-- /.row -->
        
        <!-- Main row -->
        <div class="row">
          <!-- Left col -->
          <section class="col-lg-7 connectedSortable">
            <!-- Custom tabs (Charts with tabs)-->
            <div class="card">
              <div class="card-header">
                <h3 class="card-title">
                  <i class="fas fa-chart-pie mr-1"></i>
                  รายงานการขาย
                </h3>
                <div class="card-tools">
                  <ul class="nav nav-pills ml-auto">
                    <li class="nav-item">
                      <a class="nav-link active" href="#revenue-chart" data-toggle="tab">พื้นที่</a>
                    </li>
                    <li class="nav-item">
                      <a class="nav-link" href="#sales-chart" data-toggle="tab">โดนัท</a>
                    </li>
                  </ul>
                </div>
              </div><!-- /.card-header -->
              <div class="card-body">
                <div class="tab-content p-0">
                  <!-- Morris chart - Sales -->
                  <div class="chart tab-pane active" id="revenue-chart"
                       style="position: relative; height: 300px;">
                    <canvas id="revenue-chart-canvas" height="300" style="height: 300px;"></canvas>
                  </div>
                  <div class="chart tab-pane" id="sales-chart" style="position: relative; height: 300px;">
                    <canvas id="sales-chart-canvas" height="300" style="height: 300px;"></canvas>
                  </div>
                </div>
              </div><!-- /.card-body -->
            </div>
            <!-- /.card -->

            <!-- DIRECT CHAT -->
            <div class="card direct-chat direct-chat-primary">
              <div class="card-header">
                <h3 class="card-title">แชทโดยตรง</h3>

                <div class="card-tools">
                  <span title="3 ข้อความใหม่" class="badge badge-primary">3</span>
                  <button type="button" class="btn btn-tool" data-card-widget="collapse">
                    <i class="fas fa-minus"></i>
                  </button>
                  <button type="button" class="btn btn-tool" title="ติดต่อ" data-widget="chat-pane-toggle">
                    <i class="fas fa-comments"></i>
                  </button>
                  <button type="button" class="btn btn-tool" data-card-widget="remove">
                    <i class="fas fa-times"></i>
                  </button>
                </div>
              </div>
              <!-- /.card-header -->
              <div class="card-body">
                <!-- Conversations are loaded here -->
                <div class="direct-chat-messages">
                  <!-- Message. Default to the left -->
                  <div class="direct-chat-msg">
                    <div class="direct-chat-infos clearfix">
                      <span class="direct-chat-name float-left">ดิเรกเตอร์สหกรณ์</span>
                      <span class="direct-chat-timestamp float-right">23 ม.ค. 2:00 น.</span>
                    </div>
                    <!-- /.direct-chat-infos -->
                    <img class="direct-chat-img" src="assets/img/user1-128x128.jpg" alt="message user image">
                    <!-- /.direct-chat-img -->
                    <div class="direct-chat-text">
                      ประชุมคณะกรรมการวันจันทร์นี้เวลา 10:00 น.
                    </div>
                    <!-- /.direct-chat-text -->
                  </div>
                  <!-- /.direct-chat-msg -->

                  <!-- Message to the right -->
                  <div class="direct-chat-msg right">
                    <div class="direct-chat-infos clearfix">
                      <span class="direct-chat-name float-right">คุณ</span>
                      <span class="direct-chat-timestamp float-left">23 ม.ค. 2:05 น.</span>
                    </div>
                    <!-- /.direct-chat-infos -->
                    <img class="direct-chat-img" src="assets/img/user3-128x128.jpg" alt="message user image">
                    <!-- /.direct-chat-img -->
                    <div class="direct-chat-text">
                      รับทราบครับ จะเตรียมเอกสารไว้ให้
                    </div>
                    <!-- /.direct-chat-text -->
                  </div>
                  <!-- /.direct-chat-msg -->

                  <!-- Message. Default to the left -->
                  <div class="direct-chat-msg">
                    <div class="direct-chat-infos clearfix">
                      <span class="direct-chat-name float-left">ดิเรกเตอร์สหกรณ์</span>
                      <span class="direct-chat-timestamp float-right">23 ม.ค. 2:10 น.</span>
                    </div>
                    <!-- /.direct-chat-infos -->
                    <img class="direct-chat-img" src="assets/img/user1-128x128.jpg" alt="message user image">
                    <!-- /.direct-chat-img -->
                    <div class="direct-chat-text">
                      ขอบคุณครับ
                    </div>
                    <!-- /.direct-chat-text -->
                  </div>
                  <!-- /.direct-chat-msg -->

                </div>
                <!--/.direct-chat-messages-->

                <!-- Contacts are loaded here -->
                <div class="direct-chat-contacts">
                  <ul class="contacts-list">
                    <li>
                      <a href="#">
                        <img class="contacts-list-img" src="assets/img/user1-128x128.jpg" alt="User Avatar">

                        <div class="contacts-list-info">
                          <span class="contacts-list-name">
                            คุณ กิตติ
                            <small class="contacts-list-date float-right">2/28/2015</small>
                          </span>
                          <span class="contacts-list-msg">วิธีการเปลี่ยนแปลงที่ฉันสามารถดำเนินการ</span>
                        </div>
                        <!-- /.contacts-list-info -->
                      </a>
                    </li>
                    <!-- End Contact Item -->
                    <li>
                      <a href="#">
                        <img class="contacts-list-img" src="assets/img/user7-128x128.jpg" alt="User Avatar">

                        <div class="contacts-list-info">
                          <span class="contacts-list-name">
                            คุณ สมหมาย
                            <small class="contacts-list-date float-right">2/23/2015</small>
                          </span>
                          <span class="contacts-list-msg">ฉันจะใช้เฟรมเวิร์กในโครงการใหม่นี้ไหม</span>
                        </div>
                        <!-- /.contacts-list-info -->
                      </a>
                    </li>
                    <!-- End Contact Item -->
                  </ul>
                  <!-- /.contacts-list -->
                </div>
                <!-- /.direct-chat-pane -->
              </div>
              <!-- /.card-body -->
              <div class="card-footer">
                <form action="#" method="post">
                  <div class="input-group">
                    <input type="text" name="message" placeholder="พิมพ์ข้อความ ..." class="form-control">
                    <span class="input-group-append">
                      <button type="button" class="btn btn-primary">ส่ง</button>
                    </span>
                  </div>
                </form>
              </div>
              <!-- /.card-footer-->
            </div>
            <!--/.direct-chat -->

            <!-- TO DO List -->
            <div class="card">
              <div class="card-header">
                <h3 class="card-title">
                  <i class="ion ion-clipboard mr-1"></i>
                  รายการสิ่งที่ต้องทำ
                </h3>

                <div class="card-tools">
                  <ul class="pagination pagination-sm">
                    <li class="page-item"><a href="#" class="page-link">&laquo;</a></li>
                    <li class="page-item"><a href="#" class="page-link">1</a></li>
                    <li class="page-item"><a href="#" class="page-link">2</a></li>
                    <li class="page-item"><a href="#" class="page-link">3</a></li>
                    <li class="page-item"><a href="#" class="page-link">&raquo;</a></li>
                  </ul>
                </div>
              </div>
              <!-- /.card-header -->
              <div class="card-body">
                <ul class="todo-list" data-widget="todo-list">
                  <li>
                    <!-- drag handle -->
                    <span class="handle">
                      <i class="fas fa-ellipsis-v"></i>
                      <i class="fas fa-ellipsis-v"></i>
                    </span>
                    <!-- checkbox -->
                    <div  class="icheck-primary d-inline ml-2">
                      <input type="checkbox" value="" name="todo1" id="todoCheck1">
                      <label for="todoCheck1"></label>
                    </div>
                    <!-- todo text -->
                    <span class="text">อัพเดทข้อมูลสมาชิก</span>
                    <!-- Emphasis label -->
                    <small class="badge badge-danger"><i class="far fa-clock"></i> 2 วัน</small>
                    <!-- General tools such as edit or delete-->
                    <div class="tools">
                      <i class="fas fa-edit"></i>
                      <i class="fas fa-trash-o"></i>
                    </div>
                  </li>
                  <li>
                    <span class="handle">
                      <i class="fas fa-ellipsis-v"></i>
                      <i class="fas fa-ellipsis-v"></i>
                    </span>
                    <div  class="icheck-primary d-inline ml-2">
                      <input type="checkbox" value="" name="todo2" id="todoCheck2" checked>
                      <label for="todoCheck2"></label>
                    </div>
                    <span class="text">ประชุมคณะกรรมการ</span>
                    <small class="badge badge-info"><i class="far fa-clock"></i> 4 ชั่วโมง</small>
                    <div class="tools">
                      <i class="fas fa-edit"></i>
                      <i class="fas fa-trash-o"></i>
                    </div>
                  </li>
                  <li>
                    <span class="handle">
                      <i class="fas fa-ellipsis-v"></i>
                      <i class="fas fa-ellipsis-v"></i>
                    </span>
                    <div  class="icheck-primary d-inline ml-2">
                      <input type="checkbox" value="" name="todo3" id="todoCheck3">
                      <label for="todoCheck3"></label>
                    </div>
                    <span class="text">ตรวจสอบงบการเงิน</span>
                    <small class="badge badge-warning"><i class="far fa-clock"></i> 1 วัน</small>
                    <div class="tools">
                      <i class="fas fa-edit"></i>
                      <i class="fas fa-trash-o"></i>
                    </div>
                  </li>
                  <li>
                    <span class="handle">
                      <i class="fas fa-ellipsis-v"></i>
                      <i class="fas fa-ellipsis-v"></i>
                    </span>
                    <div  class="icheck-primary d-inline ml-2">
                      <input type="checkbox" value="" name="todo4" id="todoCheck4">
                      <label for="todoCheck4"></label>
                    </div>
                    <span class="text">เตรียมรายงานประจำเดือน</span>
                    <small class="badge badge-success"><i class="far fa-clock"></i> 3 วัน</small>
                    <div class="tools">
                      <i class="fas fa-edit"></i>
                      <i class="fas fa-trash-o"></i>
                    </div>
                  </li>
                  <li>
                    <span class="handle">
                      <i class="fas fa-ellipsis-v"></i>
                      <i class="fas fa-ellipsis-v"></i>
                    </span>
                    <div  class="icheck-primary d-inline ml-2">
                      <input type="checkbox" value="" name="todo5" id="todoCheck5">
                      <label for="todoCheck5"></label>
                    </div>
                    <span class="text">จัดกิจกรรมสมาชิก</span>
                    <small class="badge badge-primary"><i class="far fa-clock"></i> 1 สัปดาห์</small>
                    <div class="tools">
                      <i class="fas fa-edit"></i>
                      <i class="fas fa-trash-o"></i>
                    </div>
                  </li>
                  <li>
                    <span class="handle">
                      <i class="fas fa-ellipsis-v"></i>
                      <i class="fas fa-ellipsis-v"></i>
                    </span>
                    <div  class="icheck-primary d-inline ml-2">
                      <input type="checkbox" value="" name="todo6" id="todoCheck6">
                      <label for="todoCheck6"></label>
                    </div>
                    <span class="text">อบรมพนักงาน</span>
                    <small class="badge badge-secondary"><i class="far fa-clock"></i> 2 สัปดาห์</small>
                    <div class="tools">
                      <i class="fas fa-edit"></i>
                      <i class="fas fa-trash-o"></i>
                    </div>
                  </li>
                </ul>
              </div>
              <!-- /.card-body -->
              <div class="card-footer clearfix">
                <button type="button" class="btn btn-primary float-right"><i class="fas fa-plus"></i> เพิ่มรายการ</button>
              </div>
            </div>
            <!-- /.card -->
          </section>
          <!-- /.Left col -->
          <!-- right col (We are only adding the ID to make the widgets sortable)-->
          <section class="col-lg-5 connectedSortable">

            <!-- Map card -->
            <div class="card bg-gradient-primary">
              <div class="card-header border-0">
                <h3 class="card-title">
                  <i class="fas fa-map-marker-alt mr-1"></i>
                  ผู้เยี่ยมชม
                </h3>
                <!-- card tools -->
                <div class="card-tools">
                  <button type="button" class="btn btn-primary btn-sm daterange" title="Date range">
                    <i class="far fa-calendar-alt"></i>
                  </button>
                  <button type="button" class="btn btn-primary btn-sm" data-card-widget="collapse" title="Collapse">
                    <i class="fas fa-minus"></i>
                  </button>
                </div>
                <!-- /.card-tools -->
              </div>
              <div class="card-body">
                <div id="world-map" style="height: 250px; width: 100%;"></div>
              </div>
              <!-- /.card-body-->
              <div class="card-footer bg-transparent">
                <div class="row">
                  <div class="col-4 text-center">
                    <div id="sparkline-1"></div>
                    <div class="text-white">ผู้เข้าชมออนไลน์</div>
                  </div>
                  <!-- ./col -->
                  <div class="col-4 text-center">
                    <div id="sparkline-2"></div>
                    <div class="text-white">อัตราการเด้ง</div>
                  </div>
                  <!-- ./col -->
                  <div class="col-4 text-center">
                    <div id="sparkline-3"></div>
                    <div class="text-white">ผู้เข้าชม</div>
                  </div>
                  <!-- ./col -->
                </div>
                <!-- /.row -->
              </div>
            </div>
            <!-- /.card -->

            <!-- solid sales graph -->
            <div class="card bg-gradient-info">
              <div class="card-header border-0">
                <h3 class="card-title">
                  <i class="fas fa-th mr-1"></i>
                  กราฟการขาย
                </h3>

                <div class="card-tools">
                  <button type="button" class="btn bg-info btn-sm" data-card-widget="collapse">
                    <i class="fas fa-minus"></i>
                  </button>
                  <button type="button" class="btn bg-info btn-sm" data-card-widget="remove">
                    <i class="fas fa-times"></i>
                  </button>
                </div>
              </div>
              <div class="card-body">
                <canvas class="chart" id="line-chart" style="min-height: 250px; height: 250px; max-height: 250px; max-width: 100%;"></canvas>
              </div>
              <!-- /.card-body -->
              <div class="card-footer bg-transparent">
                <div class="row">
                  <div class="col-4 text-center">
                    <input type="text" class="knob" readonly value="20" data-width="60" data-height="60"
                           data-fgColor="#39CCCC">

                    <div class="text-white">Mail-Orders</div>
                  </div>
                  <!-- ./col -->
                  <div class="col-4 text-center">
                    <input type="text" class="knob" readonly value="50" data-width="60" data-height="60"
                           data-fgColor="#39CCCC">

                    <div class="text-white">Online</div>
                  </div>
                  <!-- ./col -->
                  <div class="col-4 text-center">
                    <input type="text" class="knob" readonly value="30" data-width="60" data-height="60"
                           data-fgColor="#39CCCC">

                    <div class="text-white">In-Store</div>
                  </div>
                  <!-- ./col -->
                </div>
                <!-- /.row -->
              </div>
              <!-- /.card-footer -->
            </div>
            <!-- /.card -->

            <!-- Calendar -->
            <div class="card bg-gradient-success">
              <div class="card-header border-0">

                <h3 class="card-title">
                  <i class="far fa-calendar-alt"></i>
                  ปฏิทิน
                </h3>
                <!-- tools card -->
                <div class="card-tools">
                  <!-- button with a dropdown -->
                  <div class="btn-group">
                    <button type="button" class="btn btn-success btn-sm dropdown-toggle" data-toggle="dropdown" data-offset="-52">
                      <i class="fas fa-bars"></i>
                    </button>
                    <div class="dropdown-menu" role="menu">
                      <a href="#" class="dropdown-item">เพิ่มเหตุการณ์ใหม่</a>
                      <a href="#" class="dropdown-item">ล้างเหตุการณ์</a>
                      <div class="dropdown-divider"></div>
                      <a href="#" class="dropdown-item">ดูปฏิทิน</a>
                    </div>
                  </div>
                  <button type="button" class="btn btn-success btn-sm" data-card-widget="collapse">
                    <i class="fas fa-minus"></i>
                  </button>
                  <button type="button" class="btn btn-success btn-sm" data-card-widget="remove">
                    <i class="fas fa-times"></i>
                  </button>
                </div>
                <!-- /. tools -->
              </div>
              <!-- /.card-header -->
              <div class="card-body pt-0">
                <!--The calendar -->
                <div id="calendar" style="width: 100%"></div>
              </div>
              <!-- /.card-body -->
            </div>
            <!-- /.card -->
          </section>
          <!-- right col -->
        </div>
        <!-- /.row (main row) -->
      </div><!-- /.container-fluid -->
    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
  
  <?php include('footer_adminlte.php'); ?>

<!-- ./wrapper -->

<script>
$(function () {
  /* ChartJS
   * -------
   * Here we will create a few charts using ChartJS
   */

  //--------------
  //- AREA CHART -
  //--------------

  // Get context with jQuery - using jQuery's .get() method.
  var areaChartCanvas = $('#revenue-chart-canvas').get(0).getContext('2d')

  var areaChartData = {
    labels  : ['January', 'February', 'March', 'April', 'May', 'June', 'July'],
    datasets: [
      {
        label               : 'Digital Goods',
        backgroundColor     : 'rgba(60,141,188,0.9)',
        borderColor         : 'rgba(60,141,188,0.8)',
        pointRadius          : false,
        pointColor          : '#3b8bba',
        pointStrokeColor    : 'rgba(60,141,188,1)',
        pointHighlightFill  : '#fff',
        pointHighlightStroke: 'rgba(60,141,188,1)',
        data                : [28, 48, 40, 19, 86, 27, 90]
      },
      {
        label               : 'Electronics',
        backgroundColor     : 'rgba(210, 214, 222, 1)',
        borderColor         : 'rgba(210, 214, 222, 1)',
        pointRadius         : false,
        pointColor          : 'rgba(210, 214, 222, 1)',
        pointStrokeColor    : '#c1c7d1',
        pointHighlightFill  : '#fff',
        pointHighlightStroke: 'rgba(220,220,220,1)',
        data                : [65, 59, 80, 81, 56, 55, 40]
      },
    ]
  }

  var areaChartOptions = {
    maintainAspectRatio : false,
    responsive : true,
    legend: {
      display: false
    },
    scales: {
      xAxes: [{
        gridLines : {
          display : false,
        }
      }],
      yAxes: [{
        gridLines : {
          display : false,
        }
      }]
    }
  }

  // This will get the first returned node in the jQuery collection.
  new Chart(areaChartCanvas, {
    type: 'line',
    data: areaChartData,
    options: areaChartOptions
  })

  //-------------
  //- LINE CHART -
  //--------------
  var lineChartCanvas = $('#line-chart').get(0).getContext('2d')
  var lineChartOptions = $.extend(true, {}, areaChartOptions)
  var lineChartData = $.extend(true, {}, areaChartData)
  lineChartData.datasets[0].fill = false;
  lineChartData.datasets[1].fill = false;
  lineChartOptions.datasetFill = false

  var lineChart = new Chart(lineChartCanvas, {
    type: 'line',
    data: lineChartData,
    options: lineChartOptions
  })

  //-------------
  //- DONUT CHART -
  //-------------
  // Get context with jQuery - using jQuery's .get() method.
  var donutChartCanvas = $('#sales-chart-canvas').get(0).getContext('2d')
  var donutData        = {
    labels: [
        'Chrome',
        'IE',
        'FireFox',
        'Safari',
        'Opera',
        'Navigator',
    ],
    datasets: [
      {
        data: [700,500,400,600,300,100],
        backgroundColor : ['#f56954', '#00a65a', '#f39c12', '#00c0ef', '#3c8dbc', '#d2d6de'],
      }
    ]
  }
  var donutOptions     = {
    maintainAspectRatio : false,
    responsive : true,
  }
  //Create pie or douhnut chart
  // You can switch between pie and douhnut using the method below.
  new Chart(donutChartCanvas, {
    type: 'doughnut',
    data: donutData,
    options: donutOptions
  })

  //-------------
  //- PIE CHART -
  //-------------
  // Get context with jQuery - using jQuery's .get() method.
  var pieChartCanvas = $('#pieChart').get(0).getContext('2d')
  var pieData        = donutData;
  var pieOptions     = {
    maintainAspectRatio : false,
    responsive : true,
  }
  //Create pie or douhnut chart
  // You can switch between pie and douhnut using the method below.
  new Chart(pieChartCanvas, {
    type: 'pie',
    data: pieData,
    options: pieOptions
  })

  //-----------------
  //- END PIE CHART -
  //-----------------

  /* jVector Maps
   * ------------
   * Create a world map with markers
   */
  $('#world-map').vectorMap({
    map              : 'world_mill_en',
    normalizeFunction: 'polynomial',
    hoverOpacity     : 0.7,
    hoverColor       : false,
    backgroundColor  : 'transparent',
    regionStyle      : {
      initial      : {
        fill            : 'rgba(255, 255, 255, 0.7)',
        'fill-opacity'  : 1,
        stroke          : 'rgba(0, 0, 0, 0.2)',
        'stroke-width'  : 1,
        'stroke-opacity': 1
      }
    },
    markerStyle      : {
      initial: {
        r    : 9,
        'fill'           : '#fff',
        'fill-opacity'   : 1,
        'stroke'         : '#000',
        'stroke-width'   : 5,
        'stroke-opacity' : 0.4,
      },
    },
    markers          : [
      {
        latLng: [41.90, 12.45],
        name  : 'Vatican City'
      },
      {
        latLng: [43.73, 7.41],
        name  : 'Monaco'
      },
      {
        latLng: [-0.52, 166.93],
        name  : 'Nauru'
      },
      {
        latLng: [-8.51, 179.21],
        name  : 'Tuvalu'
      },
      {
        latLng: [43.93, 12.46],
        name  : 'San Marino'
      },
      {
        latLng: [47.14, 9.52],
        name  : 'Liechtenstein'
      },
      {
        latLng: [7.11, 171.06],
        name  : 'Marshall Islands'
      },
      {
        latLng: [17.3, -62.73],
        name  : 'Saint Kitts and Nevis'
      },
      {
        latLng: [3.2, 73.22],
        name  : 'Maldives'
      },
      {
        latLng: [35.88, 14.5],
        name  : 'Malta'
      },
      {
        latLng: [12.05, -61.75],
        name  : 'Grenada'
      },
      {
        latLng: [13.16, -61.23],
        name  : 'Saint Vincent and the Grenadines'
      },
      {
        latLng: [13.16, -59.55],
        name  : 'Barbados'
      },
      {
        latLng: [17.11, -61.85],
        name  : 'Antigua and Barbuda'
      },
      {
        latLng: [-4.61, 55.45],
        name  : 'Seychelles'
      },
      {
        latLng: [7.35, 134.46],
        name  : 'Palau'
      },
      {
        latLng: [42.5, 1.51],
        name  : 'Andorra'
      },
      {
        latLng: [14.01, -60.98],
        name  : 'Saint Lucia'
      },
      {
        latLng: [6.91, 158.18],
        name  : 'Federated States of Micronesia'
      },
      {
        latLng: [1.3, 103.8],
        name  : 'Singapore'
      },
      {
        latLng: [1.46, 173.03],
        name  : 'Kiribati'
      },
      {
        latLng: [-21.13, -175.2],
        name  : 'Tonga'
      },
      {
        latLng: [15.3, -61.38],
        name  : 'Dominica'
      },
      {
        latLng: [-20.2, 57.5],
        name  : 'Mauritius'
      },
      {
        latLng: [26.02, 50.55],
        name  : 'Bahrain'
      },
      {
        latLng: [0.33, 6.73],
        name  : 'São Tomé and Príncipe'
      }
    ]
  })

  /* SPARKLINE CHARTS
   * ----------------
   * Create a inline charts with spark line
   */

  //-----------------
  //- SPARKLINE BAR -
  //-----------------
  $('.sparkbar').each(function () {
    var $this = $(this)
    $this.sparkline('html', {
      type    : 'bar',
      height  : $this.data('height') ? $this.data('height') : '30',
      barColor: $this.data('color')
    })
  })

  //-----------------
  //- SPARKLINE PIE -
  //-----------------
  $('.sparkpie').each(function () {
    var $this = $(this)
    $this.sparkline('html', {
      type       : 'pie',
      height     : $this.data('height') ? $this.data('height') : '90',
      sliceColors: $this.data('color')
    })
  })

  //-----------------
  //- SPARKLINE LINE -
  //-----------------
  $('.sparkline').each(function () {
    var $this = $(this)
    $this.sparkline('html', {
      type     : 'line',
      height   : $this.data('height') ? $this.data('height') : '90',
      width    : '100%',
      lineColor: $this.data('linecolor'),
      fillColor: $this.data('fillcolor'),
      spotColor: $this.data('spotcolor')
    })
  })

  //---------------------
  //- SPARKLINE WIDGETS -
  //---------------------

  $('#sparkline-1').sparkline([15, 19, 20, 22, 33, 27, 31, 27, 19, 30, 21], {
    type     : 'line',
    width    : 80,
    height   : 50,
    lineColor: '#92c1dc',
    fillColor: "transparent"
  })
  $('#sparkline-2').sparkline([6, 10, 9, 11, 9, 15, 14, 10], {
    type     : 'line',
    width    : 80,
    height   : 50,
    lineColor: '#92c1dc',
    fillColor: "transparent"
  })
  $('#sparkline-3').sparkline([4, 6, 7, 7, 4, 3, 2, 1], {
    type     : 'line',
    width    : 80,
    height   : 50,
    lineColor: '#92c1dc',
    fillColor: "transparent"
  })

  //---------------
  //- MONTHLY SALES WIDGET
  //---------------

  $('#world-map-markers').vectorMap({
    map              : 'world_mill_en',
    normalizeFunction: 'polynomial',
    hoverOpacity     : 0.7,
    hoverColor       : false,
    backgroundColor  : 'transparent',
    regionStyle      : {
      initial      : {
        fill            : 'rgba(210, 214, 222, 1)',
        'fill-opacity'  : 1,
        stroke          : 'rgba(210, 214, 222, 1)',
        'stroke-width'  : 1,
        'stroke-opacity': 1
      }
    },
    markerStyle      : {
      initial: {
        r    : 5,
        'fill'           : 'rgba(255,255,255,0.8)',
        'fill-opacity'   : 1,
        'stroke'         : '#000',
        'stroke-width'   : 2,
        'stroke-opacity' : 0.4,
      },
    },
    markers          : [
      {
        latLng: [41.90, 12.45],
        name  : 'Vatican City'
      },
      {
        latLng: [43.73, 7.41],
        name  : 'Monaco'
      },
      {
        latLng: [-0.52, 166.93],
        name  : 'Nauru'
      },
      {
        latLng: [-8.51, 179.21],
        name  : 'Tuvalu'
      },
      {
        latLng: [43.93, 12.46],
        name  : 'San Marino'
      },
      {
        latLng: [47.14, 9.52],
        name  : 'Liechtenstein'
      },
      {
        latLng: [7.11, 171.06],
        name  : 'Marshall Islands'
      },
      {
        latLng: [17.3, -62.73],
        name  : 'Saint Kitts and Nevis'
      },
      {
        latLng: [3.2, 73.22],
        name  : 'Maldives'
      },
      {
        latLng: [35.88, 14.5],
        name  : 'Malta'
      },
      {
        latLng: [12.05, -61.75],
        name  : 'Grenada'
      },
      {
        latLng: [13.16, -61.23],
        name  : 'Saint Vincent and the Grenadines'
      },
      {
        latLng: [13.16, -59.55],
        name  : 'Barbados'
      },
      {
        latLng: [17.11, -61.85],
        name  : 'Antigua and Barbuda'
      },
      {
        latLng: [-4.61, 55.45],
        name  : 'Seychelles'
      },
      {
        latLng: [7.35, 134.46],
        name  : 'Palau'
      },
      {
        latLng: [42.5, 1.51],
        name  : 'Andorra'
      },
      {
        latLng: [14.01, -60.98],
        name  : 'Saint Lucia'
      },
      {
        latLng: [6.91, 158.18],
        name  : 'Federated States of Micronesia'
      },
      {
        latLng: [1.3, 103.8],
        name  : 'Singapore'
      },
      {
        latLng: [1.46, 173.03],
        name  : 'Kiribati'
      },
      {
        latLng: [-21.13, -175.2],
        name  : 'Tonga'
      },
      {
        latLng: [15.3, -61.38],
        name  : 'Dominica'
      },
      {
        latLng: [-20.2, 57.5],
        name  : 'Mauritius'
      },
      {
        latLng: [26.02, 50.55],
        name  : 'Bahrain'
      },
      {
        latLng: [0.33, 6.73],
        name  : 'São Tomé and Príncipe'
      }
    ]
  })

  //-----------------
  //- END WORLD MAP -
  //-----------------

  /* jQueryKnob */

  $('.knob').knob({
    /*change : function (value) {
     //console.log("change : " + value);
     },
     release : function (value) {
     console.log("release : " + value);
     },
     cancel : function () {
     console.log("cancel : " + this.value);
     },*/
    draw: function () {

      // "tron" case
      if (this.$.data('skin') == 'tron') {

        var a   = this.angle(this.cv)  // Angle
          ,
            sa  = this.startAngle          // Previous start angle
          ,
            sat = this.startAngle         // Start angle
          ,
            ea                            // Previous end angle
          ,
            eat = sat + a                 // End angle
          ,
            r   = true

        this.g.lineWidth = this.lineWidth

        this.o.cursor
        && (sat = eat - 0.3)
        && (eat = eat + 0.3)

        if (this.o.displayPrevious) {
          ea = this.startAngle + this.angle(this.value)
          this.o.cursor
          && (sa = ea - 0.3)
          && (ea = ea + 0.3)
          this.g.beginPath()
          this.g.strokeStyle = this.previousColor
          this.g.arc(this.xy, this.xy, this.radius - this.lineWidth, sa, ea, false)
          this.g.stroke()
        }

        this.g.beginPath()
        this.g.strokeStyle = r ? this.o.fgColor : this.fgColor
        this.g.arc(this.xy, this.xy, this.radius - this.lineWidth, sat, eat, false)
        this.g.stroke()

        this.g.lineWidth = 2
        this.g.beginPath()
        this.g.strokeStyle = this.o.fgColor
        this.g.arc(this.xy, this.xy, this.radius - this.lineWidth + 1 + this.lineWidth * 2 / 3, 0, 2 * Math.PI, false)
        this.g.stroke()

        return false
      }
    }
  })
  /* END JQUERY KNOB */

  //INITIALIZE SPARKLINE CHARTS
  var sparkline1 = new Sparkline($('#sparkline-1')[0], {width: 240, height: 70, lineColor: '#92c1dc', endColor: '#92c1dc'})
  var sparkline2 = new Sparkline($('#sparkline-2')[0], {width: 240, height: 70, lineColor: '#92c1dc', endColor: '#92c1dc'})
  var sparkline3 = new Sparkline($('#sparkline-3')[0], {width: 240, height: 70, lineColor: '#92c1dc', endColor: '#92c1dc'})

  sparkline1.draw([1000, 1200, 920, 927, 931, 1027, 819, 930, 1021])
  sparkline2.draw([515, 519, 520, 522, 652, 810, 370, 627, 319, 630, 921])
  sparkline3.draw([15, 19, 20, 22, 33, 27, 31, 27, 19, 30, 21])

  //The Calender
  $('#calendar').datetimepicker({
    format: 'L',
    inline: true
  })

  // Make the dashboard widgets sortable Using jquery UI
  $('.connectedSortable').sortable({
    placeholder         : 'sort-highlight',
    connectWith         : '.connectedSortable',
    handle              : '.card-header, .nav-tabs',
    forcePlaceholderSize: true,
    zIndex              : 999999
  })
  $('.connectedSortable .card-header').css('cursor', 'move')

  // jQuery UI sortable for the todo list
  $('.todo-list').sortable({
    placeholder         : 'sort-highlight',
    handle              : '.handle',
    forcePlaceholderSize: true,
    zIndex              : 999999
  })

})

// แสดง Welcome message เมื่อเข้าระบบครั้งแรก
<?php if($firstLogin) { ?>
$(document).ready(function() {
    Swal.fire({
        title: 'ยินดีต้อนรับ!',
        text: 'ยินดีต้อนรับสู่ระบบสหกรณ์ร้านค้า',
        icon: 'success',
        confirmButtonText: 'เข้าใช้งาน'
    });
});
<?php } ?>
</script>

</body>
</html>
