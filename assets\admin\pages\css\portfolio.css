/***
Portfolio
***/
/*Portfolio Filter*/
.mix-filter {
  list-style: none;
  margin: 0 0 20px 0;
  padding: 0;
}

.mix-filter li {
  color: #555;
  cursor: pointer;
  padding: 6px 15px;
  margin-right: 2px;
  margin-bottom: 5px;
  background: #eee;
  display: inline-block;
}

.mix-filter li:hover,
.mix-filter li.active {
  color: #fff;
  background: #0da3e2;
}

.mix-grid .mix {
  opacity: 0;
  display: none;
}

/*Portfolio Hover*/
.mix-grid .mix {
  position: relative;
  overflow: hidden;
  margin-bottom: 15px;
}

.mix-grid .mix .mix-inner {
  position: relative;
  width: 100%;
}

.mix-grid .mix .mix-details {
  padding: 5px;
  color: #fff;
  width: 100%;
  height: 100%;
  bottom: -100%;
  text-align: center;
  position: absolute;
  background: #0da3e2;
  transition: all 0.5s ease;
  -o-transition: all 0.5s ease;
  -ms-transition: all 0.5s ease;
  -moz-transition: all 0.5s ease;
  -webkit-transition: all 0.5s ease;
}

.mix-grid .mix:hover .mix-details {
  bottom: 0;
  transition: all 0.5s ease;
  -o-transition: all 0.5s ease;
  -ms-transition: all 0.5s ease;
  -moz-transition: all 0.5s ease;
  -webkit-transition: all 0.5s ease;
}

.mix-grid .mix .mix-details h4 {
  color: #fff;
  margin-top: 30px;
}

.mix-grid .mix .mix-details p {
  padding: 0 30px;
}

.mix-grid .mix .mix-details i {
  color: #fff;
  font-size: 14px;
}

.mix-grid .mix a.mix-link,
.mix-grid .mix a.mix-preview {
  color: #555;
  display: block;
  cursor: pointer;
  margin-top: 10px;
  position: absolute;
  padding: 10px 15px;
  background: #16b2f4;
}

.mix-grid .mix a.mix-preview {
  left: 50%;
  margin-left: 5px;
}

.mix-grid .mix a.mix-link {
  right: 50%;
  margin-right: 5px;
}

.mix-grid .mix a.mix-link:hover,
.mix-grid .mix a.mix-preview:hover {
  color: #fff;
  padding: 9px 14px;
  text-decoration: none;
  border: solid 1px #eee;
}

/* Portrait tablet to landscape and desktop */
@media (min-width: 992px) {
  .mix-grid .mix.col-md-6.col-sm-6 .mix-details {
    height: 50%;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .mix-grid .mix a.mix-link,
  .mix-grid .mix a.mix-preview {
    margin-top: 5px;
  }
}
