<?php

namespace App\Controllers;

use App\Models\User;
use App\Services\AuthService;

/**
 * Authentication Controller
 * จัดการการ login, logout และ authentication
 */
class AuthController extends BaseController
{
    private $userModel;
    
    public function __construct()
    {
        parent::__construct();
        $this->userModel = new User();
    }
    
    /**
     * User login
     * POST /api/auth/login
     */
    public function login()
    {
        try {
            $input = $this->sanitizeInput($this->getInput());
            
            // Validation rules
            $rules = [
                'username' => 'required|string|max:50',
                'password' => 'required|string|min:1'
            ];
            
            $validation = $this->validateInput($input, $rules);
            if (!$validation['valid']) {
                return $this->errorResponse('Validation failed', 400, $validation['errors']);
            }
            
            $username = $input['username'];
            $password = $input['password'];
            
            // Find user by username
            $user = $this->userModel->findByUsername($username);
            
            if (!$user) {
                $this->logActivity('LOGIN_FAILED', "Username: {$username} - User not found");
                return $this->errorResponse('Invalid username or password', 401);
            }
            
            // Verify password
            if (!$this->authService->verifyPassword($password, $user['UserPassword'])) {
                $this->logActivity('LOGIN_FAILED', "Username: {$username} - Invalid password");
                return $this->errorResponse('Invalid username or password', 401);
            }
            
            // Check if user is active
            if ($user['UserStatus'] !== 'เปิดใช้งาน') {
                $this->logActivity('LOGIN_FAILED', "Username: {$username} - Account disabled");
                return $this->errorResponse('Account is disabled', 401);
            }
            
            // Create session
            $sessionData = $this->authService->createSession($user);
            
            // Log successful login
            $this->userModel->logLogin($user['UserID']);
            $this->logActivity('LOGIN_SUCCESS', "Username: {$username}");
            
            return $this->successResponse([
                'user' => [
                    'id' => $user['UserID'],
                    'username' => $user['UserLoginName'],
                    'full_name' => $user['UserFullName'],
                    'group_id' => $user['UserGroupID']
                ],
                'session' => $sessionData
            ], 'Login successful');
            
        } catch (\Exception $e) {
            return $this->errorResponse('Login failed: ' . $e->getMessage());
        }
    }
    
    /**
     * User logout
     * POST /api/auth/logout
     */
    public function logout()
    {
        try {
            $user = $this->getCurrentUser();
            
            if ($user) {
                $this->logActivity('LOGOUT', "Username: {$user['UserLoginName']}");
            }
            
            $this->authService->destroySession();
            
            return $this->successResponse(null, 'Logout successful');
            
        } catch (\Exception $e) {
            return $this->errorResponse('Logout failed: ' . $e->getMessage());
        }
    }
    
    /**
     * Get current user info
     * GET /api/auth/me
     */
    public function me()
    {
        $this->requireAuth();
        
        try {
            $user = $this->getCurrentUser();
            
            if (!$user) {
                return $this->errorResponse('User not found', 404);
            }
            
            // Get user permissions
            $permissions = $this->authService->getUserPermissions($user['UserID'], $user['UserGroupID']);
            
            return $this->successResponse([
                'user' => [
                    'id' => $user['UserID'],
                    'username' => $user['UserLoginName'],
                    'full_name' => $user['UserFullName'],
                    'group_id' => $user['UserGroupID'],
                    'group_name' => $user['UserGroupName'] ?? ''
                ],
                'permissions' => $permissions
            ]);
            
        } catch (\Exception $e) {
            return $this->errorResponse('Failed to get user info: ' . $e->getMessage());
        }
    }
    
    /**
     * Change password
     * POST /api/auth/change-password
     */
    public function changePassword()
    {
        $this->requireAuth();
        $this->validateCSRF();
        
        try {
            $input = $this->sanitizeInput($this->getInput());
            $user = $this->getCurrentUser();
            
            // Validation rules
            $rules = [
                'current_password' => 'required|string',
                'new_password' => 'required|string|min:6',
                'confirm_password' => 'required|string|same:new_password'
            ];
            
            $validation = $this->validateInput($input, $rules);
            if (!$validation['valid']) {
                return $this->errorResponse('Validation failed', 400, $validation['errors']);
            }
            
            // Verify current password
            $currentUser = $this->userModel->find($user['UserID']);
            if (!$this->authService->verifyPassword($input['current_password'], $currentUser['UserPassword'])) {
                return $this->errorResponse('Current password is incorrect', 400);
            }
            
            // Hash new password
            $hashedPassword = $this->authService->hashPassword($input['new_password']);
            
            // Update password
            $updated = $this->userModel->update($user['UserID'], [
                'UserPassword' => $hashedPassword
            ]);
            
            if ($updated) {
                $this->logActivity('CHANGE_PASSWORD', "User ID: {$user['UserID']}");
                return $this->successResponse(null, 'Password changed successfully');
            } else {
                return $this->errorResponse('Failed to change password');
            }
            
        } catch (\Exception $e) {
            return $this->errorResponse('Failed to change password: ' . $e->getMessage());
        }
    }
    
    /**
     * Check session validity
     * GET /api/auth/check
     */
    public function checkSession()
    {
        try {
            $isAuthenticated = $this->authService->isAuthenticated();
            
            return $this->successResponse([
                'authenticated' => $isAuthenticated,
                'user' => $isAuthenticated ? $this->getCurrentUser() : null
            ]);
            
        } catch (\Exception $e) {
            return $this->errorResponse('Session check failed: ' . $e->getMessage());
        }
    }
    
    /**
     * Get CSRF token
     * GET /api/auth/csrf-token
     */
    public function getCsrfToken()
    {
        try {
            $token = $this->validationService->generateCSRFToken();
            
            return $this->successResponse([
                'csrf_token' => $token
            ]);
            
        } catch (\Exception $e) {
            return $this->errorResponse('Failed to generate CSRF token: ' . $e->getMessage());
        }
    }
    
    /**
     * Refresh session
     * POST /api/auth/refresh
     */
    public function refreshSession()
    {
        $this->requireAuth();
        
        try {
            $user = $this->getCurrentUser();
            $sessionData = $this->authService->refreshSession($user);
            
            return $this->successResponse([
                'session' => $sessionData
            ], 'Session refreshed');
            
        } catch (\Exception $e) {
            return $this->errorResponse('Failed to refresh session: ' . $e->getMessage());
        }
    }
    
    /**
     * Get login history
     * GET /api/auth/login-history
     */
    public function getLoginHistory()
    {
        $this->requireAuth();
        
        try {
            $user = $this->getCurrentUser();
            [$page, $perPage] = $this->getPaginationParams();
            
            $history = $this->userModel->getLoginHistory($user['UserID'], $page, $perPage);
            
            return $this->successResponse($history);
            
        } catch (\Exception $e) {
            return $this->errorResponse('Failed to get login history: ' . $e->getMessage());
        }
    }
}
