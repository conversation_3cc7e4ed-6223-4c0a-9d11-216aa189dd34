<!-- Main Footer -->
<footer class="main-footer">
  <strong>ระบบสหกรณ์ร้านค้า &copy; รัชเดช ศรีแก้ว | โทร : 093-0732896</strong>
  <div class="float-right d-none d-sm-inline-block">
    <b>เวอร์ชัน</b> 3.0.0
  </div>
</footer>

<!-- Control Sidebar -->
<aside class="control-sidebar control-sidebar-dark">
  <!-- Control sidebar content goes here -->
  <div class="p-3">
    <h5>การตั้งค่า</h5>
    <p>ปรับแต่งการแสดงผลของระบบ</p>
  </div>
</aside>
<!-- /.control-sidebar -->

<!-- jQuery -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<!-- jQuery UI 1.11.4 -->
<script src="https://code.jquery.com/ui/1.13.2/jquery-ui.min.js"></script>
<!-- Resolve conflict in jQuery UI tooltip with Bootstrap tooltip -->
<script>
  $.widget.bridge('uibutton', $.ui.button)
</script>
<!-- Bootstrap 4 -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/js/bootstrap.bundle.min.js"></script>
<!-- ChartJS -->
<script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
<!-- Sparkline -->
<script src="https://cdn.jsdelivr.net/npm/jquery-sparkline@2.4.0/jquery.sparkline.min.js"></script>
<!-- JQVMap -->
<script src="https://cdn.jsdelivr.net/npm/jqvmap@1.5.1/dist/jquery.vmap.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/jqvmap@1.5.1/dist/maps/jquery.vmap.usa.js"></script>
<!-- jQuery Knob Chart -->
<script src="https://cdn.jsdelivr.net/npm/jquery-knob@1.2.13/dist/jquery.knob.min.js"></script>
<!-- daterangepicker -->
<script src="https://cdn.jsdelivr.net/npm/moment@2.29.4/moment.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/daterangepicker@3.1.0/daterangepicker.js"></script>
<!-- Tempusdominus Bootstrap 4 -->
<script src="https://cdn.jsdelivr.net/npm/tempusdominus-bootstrap-4@5.39.0/build/js/tempusdominus-bootstrap-4.min.js"></script>
<!-- Summernote -->
<script src="https://cdn.jsdelivr.net/npm/summernote@0.8.20/dist/summernote-bs4.min.js"></script>
<!-- overlayScrollbars -->
<script src="https://cdn.jsdelivr.net/npm/overlayscrollbars@1.13.1/js/jquery.overlayScrollbars.min.js"></script>
<!-- AdminLTE App -->
<script src="https://cdn.jsdelivr.net/npm/admin-lte@3.2/dist/js/adminlte.js"></script>
<!-- SweetAlert2 -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<!-- Select2 -->
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<!-- Bootstrap4 Duallistbox -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap4-duallistbox@4.0.2/dist/jquery.bootstrap-duallistbox.min.js"></script>
<!-- InputMask -->
<script src="https://cdn.jsdelivr.net/npm/inputmask@5.0.8/dist/jquery.inputmask.min.js"></script>
<!-- date-range-picker -->
<script src="https://cdn.jsdelivr.net/npm/daterangepicker@3.1.0/daterangepicker.js"></script>
<!-- bootstrap color picker -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap-colorpicker@3.4.0/dist/js/bootstrap-colorpicker.min.js"></script>
<!-- Bootstrap Switch -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap-switch@3.3.4/dist/js/bootstrap-switch.min.js"></script>
<!-- BS-Stepper -->
<script src="https://cdn.jsdelivr.net/npm/bs-stepper@1.7.0/dist/js/bs-stepper.min.js"></script>
<!-- dropzonejs -->
<script src="https://cdn.jsdelivr.net/npm/dropzone@5.9.3/dist/min/dropzone.min.js"></script>
<!-- DataTables  & Plugins -->
<script src="https://cdn.jsdelivr.net/npm/datatables.net@1.13.1/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/datatables.net-bs4@1.13.1/js/dataTables.bootstrap4.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/datatables.net-responsive@2.4.0/js/dataTables.responsive.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/datatables.net-responsive-bs4@2.4.0/js/responsive.bootstrap4.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/datatables.net-buttons@2.3.3/js/dataTables.buttons.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/datatables.net-buttons-bs4@2.3.3/js/buttons.bootstrap4.min.js"></script>

<script>
// ฟังก์ชันแจ้งเตือนต่างๆ ด้วย SweetAlert2

// ฟังก์ชันแจ้งเตือนสำเร็จ
function showSuccess(title, text) {
    Swal.fire({
        icon: 'success',
        title: title || 'สำเร็จ',
        text: text || 'ดำเนินการเสร็จเรียบร้อยแล้ว',
        confirmButtonText: 'ตกลง'
    });
}

// ฟังก์ชันแจ้งเตือนข้อผิดพลาด
function showError(title, text) {
    Swal.fire({
        icon: 'error',
        title: title || 'เกิดข้อผิดพลาด',
        text: text || 'กรุณาลองใหม่อีกครั้ง',
        confirmButtonText: 'ตกลง'
    });
}

// ฟังก์ชันแจ้งเตือนคำเตือน
function showWarning(title, text) {
    Swal.fire({
        icon: 'warning',
        title: title || 'คำเตือน',
        text: text || 'กรุณาตรวจสอบข้อมูล',
        confirmButtonText: 'ตกลง'
    });
}

// ฟังก์ชันแจ้งเตือนข้อมูล
function showInfo(title, text) {
    Swal.fire({
        icon: 'info',
        title: title || 'ข้อมูล',
        text: text || 'ข้อมูลสำหรับคุณ',
        confirmButtonText: 'ตกลง'
    });
}

// ฟังก์ชันยืนยันการลบ
function confirmDelete(callback) {
    Swal.fire({
        title: 'ยืนยันการลบ',
        text: 'คุณต้องการลบข้อมูลนี้ใช่หรือไม่? การกระทำนี้ไม่สามารถย้อนกลับได้',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'ใช่, ลบ',
        cancelButtonText: 'ยกเลิก'
    }).then((result) => {
        if (result.isConfirmed && typeof callback === 'function') {
            callback();
        }
    });
}

// ฟังก์ชันยืนยันการดำเนินการ
function confirmAction(title, text, callback) {
    Swal.fire({
        title: title || 'ยืนยันการดำเนินการ',
        text: text || 'คุณต้องการดำเนินการต่อใช่หรือไม่?',
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'ใช่',
        cancelButtonText: 'ยกเลิก'
    }).then((result) => {
        if (result.isConfirmed && typeof callback === 'function') {
            callback();
        }
    });
}

// ฟังก์ชันยืนยันการออกจากระบบ
function confirmLogout() {
    Swal.fire({
        title: 'ยืนยันการออกจากระบบ',
        text: 'คุณต้องการออกจากระบบใช่หรือไม่?',
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'ใช่, ออกจากระบบ',
        cancelButtonText: 'ยกเลิก'
    }).then((result) => {
        if (result.isConfirmed) {
            window.location.href = 'logout.php';
        }
    });
}

// ฟังก์ชันแสดง Loading
function showLoading(title) {
    Swal.fire({
        title: title || 'กำลังดำเนินการ...',
        html: 'กรุณารอสักครู่',
        allowEscapeKey: false,
        allowOutsideClick: false,
        didOpen: () => {
            Swal.showLoading()
        }
    });
}

// ฟังก์ชันปิด Loading
function hideLoading() {
    Swal.close();
}

// เมื่อโหลดหน้าเสร็จแล้ว
$(document).ready(function() {
    // เริ่มต้น Select2
    $('.select2').select2({
        theme: 'bootstrap4'
    });
    
    // เริ่มต้น DataTables
    $('.datatable').DataTable({
        "responsive": true,
        "lengthChange": false,
        "autoWidth": false,
        "language": {
            "url": "https://cdn.datatables.net/plug-ins/1.13.1/i18n/th.json"
        }
    });
    
    // เริ่มต้น Date Range Picker
    $('.daterange').daterangepicker({
        locale: {
            format: 'DD/MM/YYYY',
            separator: ' - ',
            applyLabel: 'ตกลง',
            cancelLabel: 'ยกเลิก',
            fromLabel: 'จาก',
            toLabel: 'ถึง',
            customRangeLabel: 'กำหนดเอง',
            weekLabel: 'W',
            daysOfWeek: ['อา', 'จ', 'อ', 'พ', 'พฤ', 'ศ', 'ส'],
            monthNames: ['มกราคม', 'กุมภาพันธ์', 'มีนาคม', 'เมษายน', 'พฤษภาคม', 'มิถุนายน',
                'กรกฎาคม', 'สิงหาคม', 'กันยายน', 'ตุลาคม', 'พฤศจิกายน', 'ธันวาคม'],
            firstDay: 1
        }
    });
    
    // Toast notification position
    const Toast = Swal.mixin({
        toast: true,
        position: 'top-end',
        showConfirmButton: false,
        timer: 3000,
        timerProgressBar: true,
        didOpen: (toast) => {
            toast.addEventListener('mouseenter', Swal.stopTimer)
            toast.addEventListener('mouseleave', Swal.resumeTimer)
        }
    });
    
    // Export toast function globally
    window.showToast = function(icon, title) {
        Toast.fire({
            icon: icon,
            title: title
        });
    };
});
</script>

</body>
</html>
