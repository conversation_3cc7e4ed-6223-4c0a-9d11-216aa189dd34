/* 
Search Page
***/
/* general search form */
.search-form-default {
  margin-bottom: 25px;
  background: #f0f6fa;
  padding: 12px 14px;
}

/*search classic*/
.search-classic {
  margin-bottom: 30px;
}

.search-classic h4 {
  margin-bottom: 3px;
}

.overflow-hidden {
  overflow: hidden;
}

/*Booking Offer*/
.booking-offer {
  position: relative;
}

.booking-offer .booking-offer-in {
  top: 15px;
  left: 15px;
  right: 15px;
  color: #fff;
  padding: 15px;
  position: absolute;
  background: url(../../img/bg-opacity.png);
}

.booking-offer .booking-offer-in em {
  font-size: 14px;
  font-style: normal;
}

.booking-offer .booking-offer-in p {
  color: #fff;
  font-size: 14px;
  margin-bottom: 0;
}

.booking-offer .booking-offer-in span {
  font-size: 22px;
  display: block;
  margin-bottom: 10px;
}

.booking-app {
  margin-bottom: 10px;
}

.booking-app a {
  color: #fff;
  padding: 15px;
  display: block;
  overflow: hidden;
  background: #78ccf8;
}

.booking-app a:hover {
  background: #4d90fe;
  text-decoration: none;
}

.booking-app span {
  top: 0px;
  color: #fff;
  font-size: 20px;
  position: relative;
}

.booking-app i {
  color: #fff;
  font-size: 40px;
  line-height: 18px;
}

/*Booking Blocks (Content)*/
.booking-results {
  margin-top: 20px;
}

.booking-result {
  overflow: hidden;
}

.booking-result .booking-img {
  display: inline-block;
  float: left;
  width: 140px;
  margin-right: 10px;
}

.booking-result .booking-img .price-location li {
  color: #777;
}

.booking-result .booking-img .price-location li i {
  color: #78ccf8;
  font-size: 12px;
  margin-right: 5px;
}

.booking-result .booking-img img {
  float: left;
  width: 140px;
  height: auto;
  margin: 3px 10px 10px 0;
}

.booking-result .booking-info .stars {
  padding: 0;
  margin: 0 0 5px 0;
}

.booking-result .booking-info .stars li {
  padding: 0;
}

.booking-result .booking-info .stars li i {
  color: #f8be2c;
  cursor: pointer;
  font-size: 16px;
}

.booking-result .booking-info h2 {
  margin-top: 2px;
  font-size: 20px;
  line-height: 20px;
}

@media (max-width: 768px) {
  .booking-results,
  .search-classic {
    margin-top: 0;
  }

  .booking-result,
  .search-classic {
    padding-bottom: 5px;
    border-bottom: 1px solid #ddd;
    margin: 15px 0;
  }
}
